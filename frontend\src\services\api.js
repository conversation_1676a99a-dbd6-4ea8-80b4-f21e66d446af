import axios from "axios";

const API_URL = import.meta.env.VITE_API_URL || "http://localhost:3000/api";

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem("token");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post("/auth/login", credentials),
  register: (userData) => api.post("/auth/register", userData),
  getProfile: () => api.get("/auth/profile"),
  updateProfile: (userData) => api.put("/auth/profile", userData),
  changePassword: (passwordData) =>
    api.post("/auth/change-password", passwordData),
};

// Equipment API
export const equipmentAPI = {
  getAll: (params) => api.get("/equipment", { params }),
  getById: (id) => api.get(`/equipment/${id}`),
  getByOwner: (ownerId) => api.get(`/equipment/owner/${ownerId}`),
  create: (data) => api.post("/equipment", data),
  update: (id, data) => api.put(`/equipment/${id}`, data),
  delete: (id) => api.delete(`/equipment/${id}`),
  getCategories: () => api.get("/equipment/categories"),
  checkAvailability: (id, params) =>
    api.get(`/equipment/${id}/availability`, { params }),
};

// Rental API
export const rentalAPI = {
  getAll: (params) => api.get("/rentals", { params }),
  getById: (id) => api.get(`/rentals/${id}`),
  getByUser: (userId) => api.get(`/rentals/user/${userId}`),
  create: (data) => api.post("/rentals", data),
  updateStatus: (id, data) => api.put(`/rentals/${id}/status`, data),
  cancel: (id) => api.delete(`/rentals/${id}`),
  getUserRentals: (params) => api.get("/rentals", { params }),
  getRevenueStats: (params) => api.get("/rentals/stats/revenue", { params }),
};

// Rating API
export const ratingAPI = {
  getByEquipment: (equipmentId, params) =>
    api.get(`/ratings/equipment/${equipmentId}`, { params }),
  getByUser: () => api.get("/ratings/user"),
  getRecent: (params) => api.get("/ratings/recent", { params }),
  create: (data) => api.post("/ratings", data),
  getById: (id) => api.get(`/ratings/${id}`),
  update: (id, data) => api.put(`/ratings/${id}`, data),
  delete: (id) => api.delete(`/ratings/${id}`),
};

// User API
export const userAPI = {
  getAll: (params) => api.get("/users", { params }),
  getById: (id) => api.get(`/users/${id}`),
  update: (id, data) => api.put(`/users/${id}`, data),
  deactivate: (id) => api.delete(`/users/${id}`),
  getStats: () => api.get("/users/stats/overview"),
  updatePixKey: (data) => api.put("/users/pix-key", data),
  getPixKey: () => api.get("/users/pix-key"),
};

// Notification API
export const notificationAPI = {
  getAll: (params) => api.get("/notifications", { params }),
  markAsRead: (id) => api.patch(`/notifications/${id}/read`),
  markAllAsRead: () => api.patch("/notifications/read-all"),
  getUnreadCount: () => api.get("/notifications/unread-count"),
};

export const recommendationAPI = {
  get: (endpoint, params) =>
    api.get(`/recommendations/${endpoint}`, { params }),
  getSimilar: (equipmentId, params) =>
    api.get(`/recommendations/similar/${equipmentId}`, { params }),
  getCollaborative: (equipmentId, params) =>
    api.get(`/recommendations/collaborative/${equipmentId}`, { params }),
  getPersonalized: (params) =>
    api.get("/recommendations/personalized", { params }),
  getTrending: (params) => api.get("/recommendations/trending", { params }),
  getDashboard: (params) => api.get("/recommendations/dashboard", { params }),
  getForEquipment: (equipmentId, params) =>
    api.get(`/recommendations/equipment/${equipmentId}`, { params }),
};

// Insurance API
export const insuranceAPI = {
  getOptions: (equipmentId, params) =>
    api.get(`/insurance/options/${equipmentId}`, { params }),
  create: (data) => api.post("/insurance", data),
  getByRentalId: (rentalId) => api.get(`/insurance/rental/${rentalId}`),
  getUserPolicies: () => api.get("/insurance/user"),
  createClaim: (data) => api.post("/insurance/claims", data),
  getClaims: (policyId) => api.get(`/insurance/claims/${policyId}`),
  updateClaim: (claimId, data) =>
    api.patch(`/insurance/claims/${claimId}`, data),
  getStats: () => api.get("/insurance/stats"),
};

// Media API
export const mediaAPI = {
  uploadImage: (file) => {
    const formData = new FormData();
    formData.append("image", file);
    return api.post("/media/upload/image", formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
  },
  uploadVideo: (file) => {
    const formData = new FormData();
    formData.append("video", file);
    return api.post("/media/upload/video", formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
  },
};

export default api;
