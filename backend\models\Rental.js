const { query } = require("../database/connection");

class Rental {
  static async create(rentalData) {
    const {
      userId,
      equipmentId,
      startDate,
      endDate,
      dailyRate,
      transactionFeePercentage = 5.0,
    } = rentalData;

    // Calculate rental details
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    const totalDays =
      Math.ceil((endDateObj - startDateObj) / (1000 * 60 * 60 * 24)) + 1;
    const subtotal = dailyRate * totalDays;
    const transactionFee = subtotal * (transactionFeePercentage / 100);
    const totalAmount = subtotal + transactionFee;

    const text = `
      INSERT INTO rentals (user_id, equipment_id, start_date, end_date, total_days,
                          daily_rate, subtotal, transaction_fee, total_amount)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;

    const values = [
      userId,
      equipmentId,
      startDate,
      endDate,
      totalDays,
      dailyRate,
      subtotal,
      transactionFee,
      totalAmount,
    ];

    const result = await query(text, values);
    return result.rows[0];
  }

  static async findById(id) {
    const text = `
      SELECT r.*, 
             e.name as equipment_name, e.category as equipment_category,
             e.images as equipment_images,
             u.first_name as user_first_name, u.last_name as user_last_name,
             u.email as user_email
      FROM rentals r
      JOIN equipment e ON r.equipment_id = e.id
      JOIN users u ON r.user_id = u.id
      WHERE r.id = $1
    `;

    const result = await query(text, [id]);
    return result.rows[0];
  }

  static async findByUser(userId, filters = {}) {
    let text = `
      SELECT r.*, 
             e.name as equipment_name, e.category as equipment_category,
             e.images as equipment_images
      FROM rentals r
      JOIN equipment e ON r.equipment_id = e.id
      WHERE r.user_id = $1
    `;

    const values = [userId];
    let paramCount = 2;

    if (filters.status) {
      text += ` AND r.status = $${paramCount}`;
      values.push(filters.status);
      paramCount++;
    }

    text += " ORDER BY r.created_at DESC";

    if (filters.limit) {
      text += ` LIMIT $${paramCount}`;
      values.push(filters.limit);
      paramCount++;
    }

    if (filters.offset) {
      text += ` OFFSET $${paramCount}`;
      values.push(filters.offset);
    }

    const result = await query(text, values);
    return result.rows;
  }

  static async findAll(filters = {}) {
    let text = `
      SELECT r.*, 
             e.name as equipment_name, e.category as equipment_category,
             e.images as equipment_images,
             u.first_name as user_first_name, u.last_name as user_last_name,
             u.email as user_email
      FROM rentals r
      JOIN equipment e ON r.equipment_id = e.id
      JOIN users u ON r.user_id = u.id
      WHERE 1=1
    `;

    const values = [];
    let paramCount = 1;

    if (filters.status) {
      text += ` AND r.status = $${paramCount}`;
      values.push(filters.status);
      paramCount++;
    }

    if (filters.equipmentId) {
      text += ` AND r.equipment_id = $${paramCount}`;
      values.push(filters.equipmentId);
      paramCount++;
    }

    if (filters.startDate) {
      text += ` AND r.start_date >= $${paramCount}`;
      values.push(filters.startDate);
      paramCount++;
    }

    if (filters.endDate) {
      text += ` AND r.end_date <= $${paramCount}`;
      values.push(filters.endDate);
      paramCount++;
    }

    text += " ORDER BY r.created_at DESC";

    if (filters.limit) {
      text += ` LIMIT $${paramCount}`;
      values.push(filters.limit);
      paramCount++;
    }

    if (filters.offset) {
      text += ` OFFSET $${paramCount}`;
      values.push(filters.offset);
    }

    const result = await query(text, values);
    return result.rows;
  }

  static async updateStatus(id, status, notes = null) {
    const text = `
      UPDATE rentals 
      SET status = $1, notes = $2
      WHERE id = $3
      RETURNING *
    `;

    const result = await query(text, [status, notes, id]);
    return result.rows[0];
  }

  static async getRevenueStats(filters = {}) {
    let text = `
      SELECT 
        COUNT(*) as total_rentals,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_rentals,
        COALESCE(SUM(CASE WHEN status = 'completed' THEN total_amount END), 0) as total_revenue,
        COALESCE(SUM(CASE WHEN status = 'completed' THEN transaction_fee END), 0) as total_fees,
        COALESCE(AVG(CASE WHEN status = 'completed' THEN total_amount END), 0) as avg_rental_value
      FROM rentals
      WHERE 1=1
    `;

    const values = [];
    let paramCount = 1;

    if (filters.startDate) {
      text += ` AND created_at >= $${paramCount}`;
      values.push(filters.startDate);
      paramCount++;
    }

    if (filters.endDate) {
      text += ` AND created_at <= $${paramCount}`;
      values.push(filters.endDate);
    }

    const result = await query(text, values);
    return result.rows[0];
  }

  static async delete(id) {
    const text = "DELETE FROM rentals WHERE id = $1 RETURNING *";
    const result = await query(text, [id]);
    return result.rows[0];
  }

  // Alias for findByUser for consistency with frontend API
  static async getByUser(userId, filters = {}) {
    return this.findByUser(userId, filters);
  }
}

module.exports = Rental;
