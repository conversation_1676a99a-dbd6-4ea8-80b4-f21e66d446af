import { useState } from "react";
import { useForm } from "react-hook-form";
import { useQuery } from "react-query";
import { toast } from "react-toastify";
import { useAuth } from "../contexts/AuthContext";
import { authAPI, equipmentAPI, rentalAPI } from "../services/api";
import {
  User,
  Mail,
  Phone,
  Lock,
  Save,
  Package,
  Calendar,
  Star,
  MapPin,
} from "lucide-react";

const Profile = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [activeTab, setActiveTab] = useState("profile");
  const { user, updateUser } = useAuth();

  // Fetch user's equipment
  const { data: userEquipment } = useQuery(
    "user-equipment",
    () => equipmentAPI.getByOwner(user?.id),
    { enabled: !!user?.id }
  );

  // Fetch user's rentals
  const { data: userRentals } = useQuery(
    "user-rentals",
    () => rentalAPI.getByUser(user?.id),
    { enabled: !!user?.id }
  );

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      email: user?.email || "",
      phone: user?.phone || "",
    },
  });

  const {
    register: registerPassword,
    handleSubmit: handlePasswordSubmit,
    formState: { errors: passwordErrors },
    reset: resetPassword,
    watch,
  } = useForm();

  const newPassword = watch("newPassword");

  const formatCurrency = (value) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("pt-BR");
  };

  const onSubmit = async (data) => {
    setIsLoading(true);

    try {
      const response = await authAPI.updateProfile(data);
      updateUser(response.data.user);
      toast.success("Profile updated successfully!");
      setIsEditing(false);
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  const onPasswordSubmit = async (data) => {
    setIsLoading(true);

    try {
      await authAPI.changePassword({
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
      });
      toast.success("Password changed successfully!");
      setShowPasswordForm(false);
      resetPassword();
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to change password");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    reset({
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      email: user?.email || "",
      phone: user?.phone || "",
    });
    setIsEditing(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Meu Perfil</h1>
          <p className="text-gray-600 mt-2">
            Gerencie suas informações, equipamentos e histórico de aluguéis
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab("profile")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "profile"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <User className="w-4 h-4 inline mr-2" />
              Informações Pessoais
            </button>
            <button
              onClick={() => setActiveTab("equipment")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "equipment"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <Package className="w-4 h-4 inline mr-2" />
              Meus Equipamentos ({userEquipment?.data?.length || 0})
            </button>
            <button
              onClick={() => setActiveTab("rentals")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "rentals"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <Calendar className="w-4 h-4 inline mr-2" />
              Histórico de Aluguéis ({userRentals?.data?.length || 0})
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === "profile" && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Profile Info */}
            <div className="lg:col-span-2">
              <div className="card">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Personal Information
                  </h2>
                  {!isEditing && (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="btn btn-secondary"
                    >
                      Edit Profile
                    </button>
                  )}
                </div>

                <form onSubmit={handleSubmit(onSubmit)}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* First Name */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        First Name
                      </label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <input
                          {...register("firstName", {
                            required: "First name is required",
                            minLength: {
                              value: 2,
                              message:
                                "First name must be at least 2 characters",
                            },
                          })}
                          type="text"
                          disabled={!isEditing}
                          className={`input pl-10 ${
                            !isEditing ? "bg-gray-50" : ""
                          } ${errors.firstName ? "input-error" : ""}`}
                        />
                      </div>
                      {errors.firstName && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.firstName.message}
                        </p>
                      )}
                    </div>

                    {/* Last Name */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name
                      </label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <input
                          {...register("lastName", {
                            required: "Last name is required",
                            minLength: {
                              value: 2,
                              message:
                                "Last name must be at least 2 characters",
                            },
                          })}
                          type="text"
                          disabled={!isEditing}
                          className={`input pl-10 ${
                            !isEditing ? "bg-gray-50" : ""
                          } ${errors.lastName ? "input-error" : ""}`}
                        />
                      </div>
                      {errors.lastName && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.lastName.message}
                        </p>
                      )}
                    </div>

                    {/* Email */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address
                      </label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <input
                          {...register("email", {
                            required: "Email is required",
                            pattern: {
                              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                              message: "Invalid email address",
                            },
                          })}
                          type="email"
                          disabled={!isEditing}
                          className={`input pl-10 ${
                            !isEditing ? "bg-gray-50" : ""
                          } ${errors.email ? "input-error" : ""}`}
                        />
                      </div>
                      {errors.email && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.email.message}
                        </p>
                      )}
                    </div>

                    {/* Phone */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <input
                          {...register("phone")}
                          type="tel"
                          disabled={!isEditing}
                          className={`input pl-10 ${
                            !isEditing ? "bg-gray-50" : ""
                          }`}
                        />
                      </div>
                    </div>
                  </div>

                  {isEditing && (
                    <div className="flex justify-end space-x-4 mt-6">
                      <button
                        type="button"
                        onClick={handleCancel}
                        className="btn btn-secondary"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={isLoading}
                        className="btn btn-primary flex items-center"
                      >
                        {isLoading ? (
                          <div className="loading-spinner h-4 w-4 mr-2"></div>
                        ) : (
                          <Save className="w-4 h-4 mr-2" />
                        )}
                        Save Changes
                      </button>
                    </div>
                  )}
                </form>
              </div>

              {/* Password Change */}
              <div className="card mt-8">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Password
                  </h2>
                  {!showPasswordForm && (
                    <button
                      onClick={() => setShowPasswordForm(true)}
                      className="btn btn-secondary flex items-center"
                    >
                      <Lock className="w-4 h-4 mr-2" />
                      Change Password
                    </button>
                  )}
                </div>

                {showPasswordForm && (
                  <form onSubmit={handlePasswordSubmit(onPasswordSubmit)}>
                    <div className="space-y-4">
                      {/* Current Password */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Current Password
                        </label>
                        <input
                          {...registerPassword("currentPassword", {
                            required: "Current password is required",
                          })}
                          type="password"
                          className={`input ${
                            passwordErrors.currentPassword ? "input-error" : ""
                          }`}
                        />
                        {passwordErrors.currentPassword && (
                          <p className="mt-1 text-sm text-red-600">
                            {passwordErrors.currentPassword.message}
                          </p>
                        )}
                      </div>

                      {/* New Password */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          New Password
                        </label>
                        <input
                          {...registerPassword("newPassword", {
                            required: "New password is required",
                            minLength: {
                              value: 6,
                              message: "Password must be at least 6 characters",
                            },
                          })}
                          type="password"
                          className={`input ${
                            passwordErrors.newPassword ? "input-error" : ""
                          }`}
                        />
                        {passwordErrors.newPassword && (
                          <p className="mt-1 text-sm text-red-600">
                            {passwordErrors.newPassword.message}
                          </p>
                        )}
                      </div>

                      {/* Confirm New Password */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Confirm New Password
                        </label>
                        <input
                          {...registerPassword("confirmPassword", {
                            required: "Please confirm your new password",
                            validate: (value) =>
                              value === newPassword || "Passwords do not match",
                          })}
                          type="password"
                          className={`input ${
                            passwordErrors.confirmPassword ? "input-error" : ""
                          }`}
                        />
                        {passwordErrors.confirmPassword && (
                          <p className="mt-1 text-sm text-red-600">
                            {passwordErrors.confirmPassword.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex justify-end space-x-4 mt-6">
                      <button
                        type="button"
                        onClick={() => {
                          setShowPasswordForm(false);
                          resetPassword();
                        }}
                        className="btn btn-secondary"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={isLoading}
                        className="btn btn-primary"
                      >
                        {isLoading ? (
                          <div className="loading-spinner h-4 w-4"></div>
                        ) : (
                          "Update Password"
                        )}
                      </button>
                    </div>
                  </form>
                )}
              </div>
            </div>

            {/* Account Summary */}
            <div className="lg:col-span-1">
              <div className="card">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  Account Summary
                </h2>

                <div className="space-y-4">
                  <div className="flex items-center justify-between py-2 border-b border-gray-200">
                    <span className="text-gray-600">Account Type</span>
                    <span
                      className={`badge ${
                        user?.role === "admin" ? "badge-info" : "badge-success"
                      }`}
                    >
                      {user?.role === "admin" ? "Administrator" : "User"}
                    </span>
                  </div>

                  <div className="flex items-center justify-between py-2 border-b border-gray-200">
                    <span className="text-gray-600">Member Since</span>
                    <span className="text-gray-900">
                      {user?.created_at
                        ? new Date(user.created_at).toLocaleDateString()
                        : "N/A"}
                    </span>
                  </div>

                  <div className="flex items-center justify-between py-2 border-b border-gray-200">
                    <span className="text-gray-600">Account Status</span>
                    <span className="badge badge-success">Active</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Equipment Tab */}
        {activeTab === "equipment" && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                Meus Equipamentos
              </h2>

              {userEquipment?.data?.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {userEquipment.data.map((equipment) => (
                    <div
                      key={equipment.id}
                      className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="aspect-w-16 aspect-h-9 mb-4">
                        <img
                          src={
                            equipment.images?.[0] ||
                            "/placeholder-equipment.jpg"
                          }
                          alt={equipment.name}
                          className="w-full h-32 object-cover rounded-lg"
                        />
                      </div>

                      <h3 className="font-medium text-gray-900 mb-2">
                        {equipment.name}
                      </h3>

                      <div className="space-y-2 text-sm text-gray-600">
                        <div className="flex items-center">
                          <Package className="w-4 h-4 mr-2" />
                          <span>{equipment.category}</span>
                        </div>

                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-2" />
                          <span>
                            {equipment.location || "Localização não informada"}
                          </span>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="font-medium text-blue-600">
                            {formatCurrency(equipment.daily_rate)}/dia
                          </span>

                          {equipment.average_rating && (
                            <div className="flex items-center">
                              <Star className="w-4 h-4 text-yellow-400 fill-current mr-1" />
                              <span>
                                {Number(equipment.average_rating).toFixed(1)}
                              </span>
                            </div>
                          )}
                        </div>

                        <div className="text-xs text-gray-500">
                          Status:{" "}
                          {equipment.status === "available"
                            ? "Disponível"
                            : "Indisponível"}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">
                    Você ainda não cadastrou nenhum equipamento.
                  </p>
                  <button
                    onClick={() => (window.location.href = "/add-equipment")}
                    className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Cadastrar Primeiro Equipamento
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Rentals Tab */}
        {activeTab === "rentals" && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                Histórico de Aluguéis
              </h2>

              {userRentals?.data?.length > 0 ? (
                <div className="space-y-4">
                  {userRentals.data.map((rental) => (
                    <div
                      key={rental.id}
                      className="border border-gray-200 rounded-lg p-4"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900 mb-2">
                            {rental.equipment_name || "Equipamento"}
                          </h3>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 mr-2" />
                              <span>
                                {formatDate(rental.start_date)} -{" "}
                                {formatDate(rental.end_date)}
                              </span>
                            </div>

                            <div className="flex items-center">
                              <span className="font-medium">
                                Total: {formatCurrency(rental.total_cost)}
                              </span>
                            </div>

                            <div className="flex items-center">
                              <span
                                className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  rental.status === "completed"
                                    ? "bg-green-100 text-green-800"
                                    : rental.status === "active"
                                    ? "bg-blue-100 text-blue-800"
                                    : rental.status === "pending"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : "bg-gray-100 text-gray-800"
                                }`}
                              >
                                {rental.status === "completed"
                                  ? "Concluído"
                                  : rental.status === "active"
                                  ? "Ativo"
                                  : rental.status === "pending"
                                  ? "Pendente"
                                  : rental.status}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">
                    Você ainda não realizou nenhum aluguel.
                  </p>
                  <button
                    onClick={() => (window.location.href = "/equipment")}
                    className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Explorar Equipamentos
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Profile;
