const express = require("express");
const Equipment = require("../models/Equipment");
const {
  authenticateToken,
  requireAdmin,
  optionalAuth,
} = require("../middleware/auth");
const {
  validateEquipment,
  validateUUID,
  validatePagination,
} = require("../middleware/validation");

const router = express.Router();

// @route   GET /api/equipment
// @desc    Get all equipment with filters
// @access  Public
router.get("/", validatePagination, optionalAuth, async (req, res) => {
  try {
    const {
      category,
      search,
      status,
      minPrice,
      maxPrice,
      location,
      startDate,
      endDate,
      sortBy,
      page = 1,
      limit = 12,
      latitude,
      longitude,
      maxDistance,
    } = req.query;

    const offset = (page - 1) * limit;

    const filters = {
      category,
      search,
      status: status || "available",
      minPrice: minPrice ? parseFloat(minPrice) : undefined,
      maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
      location,
      startDate,
      endDate,
      sortBy,
      limit: parseInt(limit),
      offset: parseInt(offset),
      latitude: latitude ? parseFloat(latitude) : undefined,
      longitude: longitude ? parseFloat(longitude) : undefined,
      maxDistance: maxDistance ? parseFloat(maxDistance) : 50,
    };

    const equipment = await Equipment.findAll(filters);

    res.json({
      success: true,
      data: {
        equipment,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: equipment.length,
        },
      },
    });
  } catch (error) {
    console.error("Equipment fetch error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch equipment",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// @route   GET /api/equipment/categories
// @desc    Get all equipment categories
// @access  Public
router.get("/categories", async (req, res) => {
  try {
    const categories = await Equipment.getCategories();

    res.json({
      success: true,
      data: { categories },
    });
  } catch (error) {
    console.error("Categories fetch error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch categories",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// @route   GET /api/equipment/:id
// @desc    Get equipment by ID
// @access  Public
router.get("/:id", validateUUID, async (req, res) => {
  try {
    const equipment = await Equipment.findById(req.params.id);

    if (!equipment) {
      return res.status(404).json({
        success: false,
        message: "Equipment not found",
      });
    }

    res.json({
      success: true,
      data: { equipment },
    });
  } catch (error) {
    console.error("Equipment fetch error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch equipment",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// @route   POST /api/equipment
// @desc    Create new equipment
// @access  Private (Admin only)
router.post(
  "/",
  authenticateToken,
  requireAdmin,
  validateEquipment,
  async (req, res) => {
    try {
      const {
        name,
        description,
        category,
        brand,
        model,
        dailyRate,
        images,
        specifications,
        location,
      } = req.body;

      const equipment = await Equipment.create({
        name,
        description,
        category,
        brand,
        model,
        dailyRate,
        images: images || [],
        specifications: specifications || {},
        location,
        ownerId: req.user.id,
      });

      res.status(201).json({
        success: true,
        message: "Equipment created successfully",
        data: { equipment },
      });
    } catch (error) {
      console.error("Equipment creation error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to create equipment",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }
);

// @route   PUT /api/equipment/:id
// @desc    Update equipment
// @access  Private (Admin only)
router.put(
  "/:id",
  authenticateToken,
  requireAdmin,
  validateUUID,
  async (req, res) => {
    try {
      const {
        name,
        description,
        category,
        brand,
        model,
        dailyRate,
        status,
        images,
        specifications,
        location,
      } = req.body;

      // Check if equipment exists
      const existingEquipment = await Equipment.findById(req.params.id);
      if (!existingEquipment) {
        return res.status(404).json({
          success: false,
          message: "Equipment not found",
        });
      }

      const updateData = {};
      if (name !== undefined) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (category !== undefined) updateData.category = category;
      if (brand !== undefined) updateData.brand = brand;
      if (model !== undefined) updateData.model = model;
      if (dailyRate !== undefined) updateData.daily_rate = dailyRate;
      if (status !== undefined) updateData.status = status;
      if (images !== undefined) updateData.images = images;
      if (specifications !== undefined)
        updateData.specifications = specifications;
      if (location !== undefined) updateData.location = location;

      const equipment = await Equipment.update(req.params.id, updateData);

      res.json({
        success: true,
        message: "Equipment updated successfully",
        data: { equipment },
      });
    } catch (error) {
      console.error("Equipment update error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update equipment",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }
);

// @route   DELETE /api/equipment/:id
// @desc    Delete equipment
// @access  Private (Admin only)
router.delete(
  "/:id",
  authenticateToken,
  requireAdmin,
  validateUUID,
  async (req, res) => {
    try {
      const equipment = await Equipment.findById(req.params.id);

      if (!equipment) {
        return res.status(404).json({
          success: false,
          message: "Equipment not found",
        });
      }

      await Equipment.delete(req.params.id);

      res.json({
        success: true,
        message: "Equipment deleted successfully",
      });
    } catch (error) {
      console.error("Equipment deletion error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete equipment",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }
);

// @route   GET /api/equipment/:id/availability
// @desc    Check equipment availability for specific dates
// @access  Public
router.get("/:id/availability", validateUUID, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: "Start date and end date are required",
      });
    }

    const isAvailable = await Equipment.checkAvailability(
      req.params.id,
      startDate,
      endDate
    );

    res.json({
      success: true,
      data: {
        available: isAvailable,
        startDate,
        endDate,
      },
    });
  } catch (error) {
    console.error("Availability check error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to check availability",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// Get equipment by owner
router.get("/owner/:ownerId", authenticateToken, async (req, res) => {
  try {
    const { ownerId } = req.params;

    // Check if user is requesting their own equipment or is admin
    if (req.user.id !== ownerId && req.user.role !== "admin") {
      return res.status(403).json({
        success: false,
        message: "Access denied",
      });
    }

    const equipment = await Equipment.getByOwner(ownerId);

    res.json({
      success: true,
      data: equipment,
    });
  } catch (error) {
    console.error("Get equipment by owner error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get equipment",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

module.exports = router;
