# 🏗️ DOCUMENTAÇÃO COMPLETA - SISTEMA DE ALUGUEL DE EQUIPAMENTOS

## 📋 ÍNDICE
1. [<PERSON><PERSON><PERSON> Geral](#visão-geral)
2. [Arquitetura](#arquitetura)
3. [Funcionalidades Implementadas](#funcionalidades-implementadas)
4. [Sistema de Autenticação](#sistema-de-autenticação)
5. [Gestão de Equipamentos](#gestão-de-equipamentos)
6. [Sistema de Aluguéis](#sistema-de-aluguéis)
7. [Sistema de Pagamentos PIX](#sistema-de-pagamentos-pix)
8. [Sistema de Seguro](#sistema-de-seguro)
9. [Sistema de Avaliações](#sistema-de-avaliações)
10. [Sistema de Notificações](#sistema-de-notificações)
11. [Sistema de Recomendações](#sistema-de-recomendações)
12. [Upload de Mídia](#upload-de-mídia)
13. [Navegação e Interface](#navegação-e-interface)
14. [APIs e Endpoints](#apis-e-endpoints)
15. [Status de Implementação](#status-de-implementação)

---

## 🎯 VISÃO GERAL

### **Modelo de Negócio**
- **Marketplace P2P**: Usuários alugam equipamentos uns dos outros
- **Comissão**: 5% sobre cada transação
- **Pagamento**: PIX obrigatório para todas as transações
- **Seguro**: Opcional para proteção dos equipamentos

### **Tecnologias**
- **Backend**: Node.js + Express + PostgreSQL
- **Frontend**: React + Vite + Tailwind CSS
- **Containerização**: Docker + Docker Compose
- **Autenticação**: JWT + Refresh Tokens
- **Upload**: Multer + File System
- **Pagamentos**: PIX (Simulado)

---

## 🏗️ ARQUITETURA

### **Backend Structure**
```
backend/
├── config/           # Configurações (JWT, CORS, etc.)
├── database/         # Conexão e migrations
├── middleware/       # Auth, validation, error handling
├── models/          # Modelos de dados
├── routes/          # Rotas da API
├── services/        # Serviços (PIX, email, etc.)
├── uploads/         # Arquivos de mídia
└── server.js        # Servidor principal
```

### **Frontend Structure**
```
frontend/
├── src/
│   ├── components/  # Componentes reutilizáveis
│   ├── contexts/    # Contextos React
│   ├── i18n/        # Internacionalização
│   ├── pages/       # Páginas da aplicação
│   ├── services/    # APIs e serviços
│   └── utils/       # Utilitários
└── public/          # Assets estáticos
```

### **Database Schema**
```sql
-- Principais tabelas:
users                 # Usuários (locadores/locatários)
equipment            # Equipamentos disponíveis
rentals              # Aluguéis realizados
payments             # Pagamentos PIX
insurance_policies   # Apólices de seguro
insurance_claims     # Sinistros de seguro
ratings              # Avaliações e reviews
notifications        # Notificações do sistema
```

---

## ✅ FUNCIONALIDADES IMPLEMENTADAS

### **🔐 AUTENTICAÇÃO E USUÁRIOS**
- [x] Registro de usuários
- [x] Login com JWT
- [x] Refresh tokens
- [x] Perfil de usuário
- [x] Configuração de chave PIX
- [x] Upload de foto de perfil
- [x] Roles (user/admin)

### **📦 GESTÃO DE EQUIPAMENTOS**
- [x] Cadastro de equipamentos
- [x] Upload de múltiplas fotos (até 5)
- [x] Upload de vídeo opcional
- [x] Categorização
- [x] Localização
- [x] Preço por dia
- [x] Status (disponível/indisponível)
- [x] Edição e exclusão

### **📅 SISTEMA DE ALUGUÉIS**
- [x] Criação de reservas
- [x] Cálculo automático de valores
- [x] Taxa de serviço (5%)
- [x] Status de aluguéis
- [x] Histórico completo
- [x] Validação de disponibilidade

### **💰 SISTEMA DE PAGAMENTOS PIX**
- [x] Configuração de chave PIX
- [x] Geração de códigos PIX
- [x] QR Code para pagamento
- [x] Verificação de status
- [x] Histórico de pagamentos
- [x] Comissão automática (5%)

### **🛡️ SISTEMA DE SEGURO**
- [x] 3 tipos de cobertura (Básica, Padrão, Premium)
- [x] Cálculo automático de prêmios
- [x] Criação de apólices
- [x] Sistema de sinistros
- [x] Gestão de claims

### **⭐ SISTEMA DE AVALIAÇÕES**
- [x] Avaliações por estrelas (1-5)
- [x] Comentários detalhados
- [x] Média de avaliações
- [x] Histórico de reviews
- [x] Validação (apenas quem alugou)

### **🔔 SISTEMA DE NOTIFICAÇÕES**
- [x] Notificações em tempo real
- [x] Email notifications
- [x] Contador de não lidas
- [x] Marcar como lida
- [x] Histórico completo

### **🤖 SISTEMA DE RECOMENDAÇÕES**
- [x] Equipamentos similares
- [x] Filtragem colaborativa
- [x] Recomendações personalizadas
- [x] Equipamentos em alta
- [x] Algoritmo de similaridade

### **📱 INTERFACE E NAVEGAÇÃO**
- [x] Design responsivo
- [x] Internacionalização (PT/EN)
- [x] Menu de usuário completo
- [x] Dashboard personalizado
- [x] Busca avançada com filtros
- [x] Comparação de equipamentos

---

## 🔐 SISTEMA DE AUTENTICAÇÃO

### **Fluxo de Autenticação**
1. **Registro**: Email, senha, nome, telefone
2. **Login**: JWT token + refresh token
3. **Middleware**: Verificação automática de tokens
4. **Logout**: Invalidação de tokens

### **Endpoints**
```
POST /api/auth/register    # Registro
POST /api/auth/login       # Login
POST /api/auth/refresh     # Renovar token
POST /api/auth/logout      # Logout
```

### **Proteção de Rotas**
- Middleware `authenticateToken` em rotas protegidas
- Verificação de roles (user/admin)
- Validação de propriedade de recursos

---

## 📦 GESTÃO DE EQUIPAMENTOS

### **Cadastro de Equipamentos**
**Campos Obrigatórios:**
- Nome do equipamento
- Descrição
- Categoria
- Preço por dia
- Localização
- Pelo menos 1 foto

**Campos Opcionais:**
- Até 4 fotos adicionais
- 1 vídeo demonstrativo
- Especificações técnicas
- Condições de uso

### **Upload de Mídia**
- **Fotos**: JPG, PNG, GIF, WebP (máx 5MB cada)
- **Vídeos**: MP4, AVI, MOV (máx 50MB)
- **Armazenamento**: File system local
- **Preview**: Visualização antes do envio

### **Navegação**
- **Menu Usuário** → "Adicionar Equipamento"
- **Dashboard** → Card "Cadastrar Ferramenta"
- **Menu Mobile** → "Adicionar Equipamento"

### **Endpoints**
```
GET    /api/equipment           # Listar equipamentos
POST   /api/equipment           # Criar equipamento
GET    /api/equipment/:id       # Detalhes
PUT    /api/equipment/:id       # Atualizar
DELETE /api/equipment/:id       # Deletar
GET    /api/equipment/owner/:id # Por proprietário
```

---

## 📅 SISTEMA DE ALUGUÉIS

### **Fluxo de Aluguel**
1. **Seleção**: Usuário escolhe equipamento e datas
2. **Cálculo**: Sistema calcula valor total + taxa (5%)
3. **Reserva**: Criação do aluguel com status "pending_payment"
4. **Pagamento**: Geração de PIX para pagamento
5. **Confirmação**: Status muda para "confirmed" após pagamento
6. **Ativo**: Status "active" durante período de uso
7. **Finalização**: Status "completed" após devolução

### **Cálculo de Valores**
```javascript
const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
const subtotal = dailyRate * totalDays;
const transactionFee = subtotal * 0.05; // 5%
const totalAmount = subtotal + transactionFee;
```

### **Status de Aluguéis**
- `pending_payment` - Aguardando pagamento
- `confirmed` - Pagamento confirmado
- `active` - Em andamento
- `completed` - Finalizado
- `cancelled` - Cancelado

### **Endpoints**
```
GET    /api/rentals            # Listar aluguéis
POST   /api/rentals            # Criar aluguel
GET    /api/rentals/:id        # Detalhes
PUT    /api/rentals/:id/status # Atualizar status
GET    /api/rentals/user/:id   # Por usuário
```

---

## 💰 SISTEMA DE PAGAMENTOS PIX

### **Configuração PIX**
**Tipos de Chave Suportados:**
- CPF/CNPJ
- Email
- Telefone
- Chave aleatória

**Configuração no Perfil:**
1. Usuário acessa "Configurações PIX"
2. Seleciona tipo de chave
3. Insere chave PIX
4. Sistema valida e salva

### **Fluxo de Pagamento**
1. **Geração**: Sistema cria código PIX para o aluguel
2. **QR Code**: Gerado automaticamente para facilitar pagamento
3. **Verificação**: Polling para verificar status do pagamento
4. **Confirmação**: Atualização automática do status do aluguel
5. **Comissão**: 5% retido pela plataforma

### **Simulação PIX**
- Sistema simula API do banco
- Pagamentos são "confirmados" automaticamente após 30 segundos
- Webhook simulado para notificações

### **Endpoints**
```
GET  /api/pix/key              # Obter chave PIX
PUT  /api/pix/key              # Configurar chave PIX
POST /api/payments/pix/create  # Criar pagamento PIX
GET  /api/payments/pix/:id     # Status do pagamento
POST /api/payments/pix/webhook # Webhook de confirmação
```

### **Estrutura de Pagamento**
```javascript
{
  id: "uuid",
  rental_id: "uuid",
  amount: 157.50,
  pix_code: "00020126580014br.gov.bcb.pix...",
  qr_code: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  status: "pending", // pending, completed, failed
  expires_at: "2024-01-01T12:00:00Z"
}
```

---

## 🛡️ SISTEMA DE SEGURO

### **Tipos de Cobertura**

**1. Cobertura Básica (5% do aluguel)**
- Danos acidentais
- Defeitos de funcionamento
- Suporte 24h
- Franquia: 2x valor diário

**2. Cobertura Padrão (8% do aluguel)**
- Tudo da básica +
- Roubo e furto
- Reposição rápida
- Franquia: 1.5x valor diário

**3. Cobertura Premium (12% do aluguel)**
- Cobertura total
- Sem franquia
- Suporte prioritário
- Reposição imediata
- Responsabilidade civil

### **Cálculo de Prêmios**
```javascript
const premiumAmount = rentalCost * premiumPercentage;
// Básica: 5%, Padrão: 8%, Premium: 12%
```

### **Sistema de Sinistros**
1. **Abertura**: Usuário reporta problema
2. **Documentação**: Upload de fotos/evidências
3. **Análise**: Avaliação do sinistro
4. **Resolução**: Aprovação/negação + valor

### **Endpoints**
```
GET  /api/insurance/options/:equipmentId  # Opções de seguro
POST /api/insurance                       # Criar apólice
POST /api/insurance/claims                # Abrir sinistro
GET  /api/insurance/user                  # Apólices do usuário
```

---

## ⭐ SISTEMA DE AVALIAÇÕES

### **Critérios de Avaliação**
- **Nota**: 1 a 5 estrelas
- **Comentário**: Texto livre (opcional)
- **Validação**: Apenas quem alugou pode avaliar
- **Timing**: Após finalização do aluguel

### **Cálculo de Média**
```javascript
const averageRating = totalRating / totalReviews;
// Atualizado automaticamente a cada nova avaliação
```

### **Endpoints**
```
GET  /api/ratings/equipment/:id  # Avaliações do equipamento
POST /api/ratings                # Criar avaliação
GET  /api/ratings/recent         # Avaliações recentes
```

---

## 🔔 SISTEMA DE NOTIFICAÇÕES

### **Tipos de Notificação**
- Novo aluguel recebido
- Pagamento confirmado
- Aluguel iniciado/finalizado
- Nova avaliação recebida
- Lembrete de devolução

### **Canais**
- **In-app**: Sino de notificações
- **Email**: Notificações importantes
- **Push**: (Futuro - PWA)

### **Endpoints**
```
GET   /api/notifications         # Listar notificações
PATCH /api/notifications/:id/read # Marcar como lida
PATCH /api/notifications/read-all  # Marcar todas como lidas
```

---

## 🤖 SISTEMA DE RECOMENDAÇÕES

### **Algoritmos Implementados**

**1. Similaridade**
- Categoria (peso 3)
- Marca (peso 2)
- Faixa de preço (peso 1-2)
- Proximidade geográfica (peso 1)

**2. Filtragem Colaborativa**
- "Quem alugou isso também alugou"
- Baseado em histórico de aluguéis

**3. Personalizado**
- Baseado no histórico do usuário
- Categorias preferidas
- Faixa de preço habitual

**4. Trending**
- Equipamentos mais alugados recentemente
- Período configurável (padrão: 30 dias)

### **Endpoints**
```
GET /api/recommendations/trending              # Em alta
GET /api/recommendations/similar/:id           # Similares
GET /api/recommendations/collaborative/:id     # Colaborativo
GET /api/recommendations/personalized          # Personalizado
GET /api/recommendations/dashboard             # Mix para dashboard
```

---

## 📱 NAVEGAÇÃO E INTERFACE

### **Menu Principal**
- **Home**: Página inicial com busca
- **Equipamentos**: Listagem com filtros
- **Como Funciona**: Informações sobre a plataforma

### **Menu do Usuário (Logado)**
- **Adicionar Equipamento**: Cadastro de novos equipamentos
- **Meus Equipamentos**: Gestão dos equipamentos próprios
- **Meus Aluguéis**: Histórico de aluguéis realizados
- **Meu Perfil**: Configurações pessoais e PIX
- **Sair**: Logout

### **Dashboard**
- **Cards de Ação**: Buscar/Cadastrar equipamentos
- **Estatísticas**: Resumo de atividades
- **Recomendações**: Equipamentos sugeridos
- **Atividades Recentes**: Últimos aluguéis/avaliações

### **Páginas Principais**
- `/` - Home
- `/equipment` - Listagem de equipamentos
- `/equipment/:id` - Detalhes do equipamento
- `/add-equipment` - Cadastro de equipamento
- `/my-equipment` - Meus equipamentos
- `/rentals` - Meus aluguéis
- `/profile` - Meu perfil
- `/compare` - Comparação de equipamentos

---

## 🔧 STATUS DE IMPLEMENTAÇÃO

### **✅ TOTALMENTE IMPLEMENTADO**
- [x] Autenticação JWT
- [x] Gestão de equipamentos
- [x] Upload de fotos/vídeos
- [x] Sistema de aluguéis
- [x] Pagamentos PIX
- [x] Sistema de seguro
- [x] Avaliações e reviews
- [x] Notificações
- [x] Recomendações inteligentes
- [x] Busca avançada
- [x] Comparação de equipamentos
- [x] Interface responsiva
- [x] Internacionalização
- [x] Menu de navegação completo

### **🔄 EM FUNCIONAMENTO**
- Backend: `http://localhost:3000`
- Frontend: `http://localhost:5173`
- Database: PostgreSQL (Docker)
- Upload: File system local

### **📋 PRÓXIMAS MELHORIAS**
- [ ] PWA (Progressive Web App)
- [ ] Push notifications
- [ ] Chat entre usuários
- [ ] Integração PIX real
- [ ] Geolocalização avançada
- [ ] Analytics e métricas

---

## 🚀 COMO USAR O SISTEMA

### **Para Locadores (Proprietários)**
1. **Cadastro**: Registre-se na plataforma
2. **PIX**: Configure sua chave PIX no perfil
3. **Equipamento**: Adicione equipamentos via menu
4. **Fotos**: Faça upload de fotos de qualidade
5. **Gestão**: Acompanhe aluguéis no dashboard

### **Para Locatários (Usuários)**
1. **Busca**: Encontre equipamentos na listagem
2. **Filtros**: Use filtros para refinar busca
3. **Detalhes**: Veja fotos, preços e avaliações
4. **Aluguel**: Selecione datas e confirme
5. **Pagamento**: Pague via PIX
6. **Avaliação**: Avalie após o uso

### **Fluxo Completo de Transação**
1. Locatário encontra equipamento
2. Seleciona datas e cria reserva
3. Sistema gera PIX para pagamento
4. Locatário paga via PIX
5. Locador é notificado
6. Equipamento é entregue
7. Após uso, locatário avalia
8. Sistema processa comissão (5%)

---

**🎉 SISTEMA 100% FUNCIONAL E DOCUMENTADO!**

Esta documentação cobre todas as funcionalidades implementadas e em funcionamento no sistema. Use como referência para evitar inconsistências e otimizar o uso de tokens.
