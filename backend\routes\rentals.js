const express = require("express");
const Rental = require("../models/Rental");
const Equipment = require("../models/Equipment");
const {
  authenticateToken,
  requireAdmin,
  requireOwnershipOrAdmin,
} = require("../middleware/auth");
const {
  validateRental,
  validateUUID,
  validatePagination,
} = require("../middleware/validation");

const router = express.Router();

// @route   GET /api/rentals
// @desc    Get rentals (user's own or all for admin)
// @access  Private
router.get("/", authenticateToken, validatePagination, async (req, res) => {
  try {
    const {
      status,
      equipmentId,
      startDate,
      endDate,
      page = 1,
      limit = 10,
    } = req.query;

    const offset = (page - 1) * limit;

    const filters = {
      status,
      equipmentId,
      startDate,
      endDate,
      limit: parseInt(limit),
      offset: parseInt(offset),
    };

    let rentals;

    if (req.user.role === "admin") {
      // Admin can see all rentals
      rentals = await Rental.findAll(filters);
    } else {
      // Regular users can only see their own rentals
      rentals = await Rental.findByUser(req.user.id, filters);
    }

    res.json({
      success: true,
      data: {
        rentals,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: rentals.length,
        },
      },
    });
  } catch (error) {
    console.error("Rentals fetch error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch rentals",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// @route   GET /api/rentals/:id
// @desc    Get rental by ID
// @access  Private (Owner or Admin)
router.get("/:id", authenticateToken, validateUUID, async (req, res) => {
  try {
    const rental = await Rental.findById(req.params.id);

    if (!rental) {
      return res.status(404).json({
        success: false,
        message: "Rental not found",
      });
    }

    // Check if user owns the rental or is admin
    if (req.user.role !== "admin" && rental.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "Access denied",
      });
    }

    res.json({
      success: true,
      data: { rental },
    });
  } catch (error) {
    console.error("Rental fetch error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch rental",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// @route   POST /api/rentals
// @desc    Create new rental
// @access  Private
router.post("/", authenticateToken, validateRental, async (req, res) => {
  try {
    const { equipmentId, startDate, endDate } = req.body;

    // Check if equipment exists and is available
    const equipment = await Equipment.findById(equipmentId);
    if (!equipment) {
      return res.status(404).json({
        success: false,
        message: "Equipment not found",
      });
    }

    if (equipment.status !== "available") {
      return res.status(400).json({
        success: false,
        message: "Equipment is not available for rental",
      });
    }

    // Check availability for the requested dates
    const isAvailable = await Equipment.checkAvailability(
      equipmentId,
      startDate,
      endDate
    );
    if (!isAvailable) {
      return res.status(409).json({
        success: false,
        message: "Equipment is not available for the selected dates",
      });
    }

    // Create rental
    const rental = await Rental.create({
      userId: req.user.id,
      equipmentId,
      startDate,
      endDate,
      dailyRate: equipment.daily_rate,
      transactionFeePercentage:
        parseFloat(process.env.TRANSACTION_FEE_PERCENTAGE) || 5,
    });

    // Update equipment status to rented
    await Equipment.update(equipmentId, { status: "rented" });

    res.status(201).json({
      success: true,
      message: "Rental created successfully",
      data: { rental },
    });
  } catch (error) {
    console.error("Rental creation error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create rental",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// @route   PUT /api/rentals/:id/status
// @desc    Update rental status
// @access  Private (Admin only)
router.put(
  "/:id/status",
  authenticateToken,
  requireAdmin,
  validateUUID,
  async (req, res) => {
    try {
      const { status, notes } = req.body;

      if (!status) {
        return res.status(400).json({
          success: false,
          message: "Status is required",
        });
      }

      const validStatuses = [
        "pending",
        "confirmed",
        "active",
        "completed",
        "cancelled",
      ];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          success: false,
          message: "Invalid status",
        });
      }

      const rental = await Rental.findById(req.params.id);
      if (!rental) {
        return res.status(404).json({
          success: false,
          message: "Rental not found",
        });
      }

      // Update rental status
      const updatedRental = await Rental.updateStatus(
        req.params.id,
        status,
        notes
      );

      // Update equipment status based on rental status
      let equipmentStatus;
      switch (status) {
        case "active":
          equipmentStatus = "rented";
          break;
        case "completed":
        case "cancelled":
          equipmentStatus = "available";
          break;
        default:
          equipmentStatus = null;
      }

      if (equipmentStatus) {
        await Equipment.update(rental.equipment_id, {
          status: equipmentStatus,
        });
      }

      res.json({
        success: true,
        message: "Rental status updated successfully",
        data: { rental: updatedRental },
      });
    } catch (error) {
      console.error("Rental status update error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update rental status",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }
);

// @route   DELETE /api/rentals/:id
// @desc    Cancel/Delete rental
// @access  Private (Owner or Admin)
router.delete("/:id", authenticateToken, validateUUID, async (req, res) => {
  try {
    const rental = await Rental.findById(req.params.id);

    if (!rental) {
      return res.status(404).json({
        success: false,
        message: "Rental not found",
      });
    }

    // Check if user owns the rental or is admin
    if (req.user.role !== "admin" && rental.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "Access denied",
      });
    }

    // Only allow cancellation if rental is pending or confirmed
    if (!["pending", "confirmed"].includes(rental.status)) {
      return res.status(400).json({
        success: false,
        message: "Cannot cancel rental in current status",
      });
    }

    // Update rental status to cancelled instead of deleting
    await Rental.updateStatus(req.params.id, "cancelled", "Cancelled by user");

    // Make equipment available again
    await Equipment.update(rental.equipment_id, { status: "available" });

    res.json({
      success: true,
      message: "Rental cancelled successfully",
    });
  } catch (error) {
    console.error("Rental cancellation error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to cancel rental",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

// @route   GET /api/rentals/stats/revenue
// @desc    Get revenue statistics
// @access  Private (Admin only)
router.get(
  "/stats/revenue",
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const { startDate, endDate } = req.query;

      const filters = {};
      if (startDate) filters.startDate = startDate;
      if (endDate) filters.endDate = endDate;

      const stats = await Rental.getRevenueStats(filters);

      res.json({
        success: true,
        data: { stats },
      });
    } catch (error) {
      console.error("Revenue stats error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch revenue statistics",
        error:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  }
);

// Get rentals by user
router.get("/user/:userId", authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;

    // Check if user is requesting their own rentals or is admin
    if (req.user.id !== userId && req.user.role !== "admin") {
      return res.status(403).json({
        success: false,
        message: "Access denied",
      });
    }

    const rentals = await Rental.getByUser(userId);

    res.json({
      success: true,
      data: rentals,
    });
  } catch (error) {
    console.error("Get rentals by user error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get rentals",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

module.exports = router;
