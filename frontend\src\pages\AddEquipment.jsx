import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import { equipmentAPI, mediaAPI } from "../services/api";
import {
  Camera,
  Video,
  MapPin,
  DollarSign,
  Package,
  FileText,
} from "lucide-react";

const AddEquipment = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [uploadingImages, setUploadingImages] = useState(false);
  const [uploadingVideo, setUploadingVideo] = useState(false);
  const [images, setImages] = useState([]);
  const [video, setVideo] = useState(null);
  const { t } = useTranslation();
  const navigate = useNavigate();

  console.log(
    "AddEquipment component rendered, images:",
    images.length,
    "video:",
    !!video
  );

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm();

  const categories = [
    "Ferramentas Elétricas",
    "Ferramentas Manuais",
    "Equipamentos de Jardim",
    "Equipamentos de Construção",
    "Equipamentos de Limpeza",
    "Equipamentos de Pintura",
    "Equipamentos de Medição",
    "Equipamentos de Segurança",
    "Outros",
  ];

  const conditions = [
    { value: "novo", label: "Novo" },
    { value: "seminovo", label: "Semi-novo" },
    { value: "usado_bom", label: "Usado - Bom Estado" },
    { value: "usado_regular", label: "Usado - Estado Regular" },
  ];

  const handleImageUpload = async (event) => {
    const files = Array.from(event.target.files);
    console.log("Files selected:", files.length);
    if (files.length === 0) return;

    setUploadingImages(true);
    try {
      const uploadPromises = files.map((file) => mediaAPI.uploadImage(file));
      const responses = await Promise.all(uploadPromises);
      console.log("Upload responses:", responses);
      const newImages = responses.map((response) => response.data.data.url);
      setImages((prev) => [...prev, ...newImages]);
      toast.success("Imagens enviadas com sucesso!");
    } catch (error) {
      console.error("Upload error:", error);
      toast.error(
        "Erro ao enviar imagens: " +
          (error.response?.data?.message || error.message)
      );
    } finally {
      setUploadingImages(false);
    }
  };

  const handleVideoUpload = async (event) => {
    const file = event.target.files[0];
    console.log("Video file selected:", file?.name);
    if (!file) return;

    setUploadingVideo(true);
    try {
      const response = await mediaAPI.uploadVideo(file);
      console.log("Video upload response:", response);
      setVideo(response.data.data.url);
      toast.success("Vídeo enviado com sucesso!");
    } catch (error) {
      console.error("Video upload error:", error);
      toast.error(
        "Erro ao enviar vídeo: " +
          (error.response?.data?.message || error.message)
      );
    } finally {
      setUploadingVideo(false);
    }
  };

  const removeImage = (index) => {
    setImages((prev) => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data) => {
    if (images.length === 0) {
      toast.error("Adicione pelo menos uma imagem do equipamento");
      return;
    }

    setIsLoading(true);
    try {
      const equipmentData = {
        ...data,
        daily_rate: parseFloat(data.daily_rate),
        images,
        video,
        specifications: {
          condition: data.condition,
          brand: data.brand,
          model: data.model,
          year: data.year,
          additional_info: data.additional_info,
        },
      };

      await equipmentAPI.create(equipmentData);
      toast.success("Equipamento cadastrado com sucesso!");
      navigate("/dashboard");
    } catch (error) {
      toast.error(
        error.response?.data?.message || "Erro ao cadastrar equipamento"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Cadastrar Equipamento
            </h1>
            <p className="text-gray-600">
              Adicione seu equipamento ao marketplace e comece a ganhar dinheiro
              alugando
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* Informações Básicas */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Package className="h-5 w-5 mr-2" />
                Informações Básicas
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nome do Equipamento *
                  </label>
                  <input
                    {...register("name", { required: "Nome é obrigatório" })}
                    type="text"
                    className="input"
                    placeholder="Ex: Furadeira Elétrica Bosch"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Categoria *
                  </label>
                  <select
                    {...register("category", {
                      required: "Categoria é obrigatória",
                    })}
                    className="input"
                  >
                    <option value="">Selecione uma categoria</option>
                    {categories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                  {errors.category && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.category.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Marca
                  </label>
                  <input
                    {...register("brand")}
                    type="text"
                    className="input"
                    placeholder="Ex: Bosch, Makita, DeWalt"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Modelo
                  </label>
                  <input
                    {...register("model")}
                    type="text"
                    className="input"
                    placeholder="Ex: GSB 550 RE"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ano
                  </label>
                  <input
                    {...register("year")}
                    type="number"
                    min="1990"
                    max={new Date().getFullYear()}
                    className="input"
                    placeholder="2023"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Estado de Conservação *
                  </label>
                  <select
                    {...register("condition", {
                      required: "Estado é obrigatório",
                    })}
                    className="input"
                  >
                    <option value="">Selecione o estado</option>
                    {conditions.map((condition) => (
                      <option key={condition.value} value={condition.value}>
                        {condition.label}
                      </option>
                    ))}
                  </select>
                  {errors.condition && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.condition.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição *
                </label>
                <textarea
                  {...register("description", {
                    required: "Descrição é obrigatória",
                  })}
                  rows={4}
                  className="input"
                  placeholder="Descreva o equipamento, suas funcionalidades, acessórios inclusos, etc."
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.description.message}
                  </p>
                )}
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Informações Adicionais
                </label>
                <textarea
                  {...register("additional_info")}
                  rows={3}
                  className="input"
                  placeholder="Cuidados especiais, instruções de uso, etc."
                />
              </div>
            </div>

            {/* Localização e Preço */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <MapPin className="h-5 w-5 mr-2" />
                Localização e Preço
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Localização *
                  </label>
                  <input
                    {...register("location", {
                      required: "Localização é obrigatória",
                    })}
                    type="text"
                    className="input"
                    placeholder="Ex: São Paulo, SP - Vila Madalena"
                  />
                  {errors.location && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.location.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preço por Dia (R$) *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <DollarSign className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      {...register("daily_rate", {
                        required: "Preço é obrigatório",
                        min: {
                          value: 1,
                          message: "Preço deve ser maior que R$ 1",
                        },
                      })}
                      type="number"
                      step="0.01"
                      min="1"
                      className="input pl-10"
                      placeholder="50.00"
                    />
                  </div>
                  {errors.daily_rate && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.daily_rate.message}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Mídia */}
            <div
              className="bg-blue-50 border-2 border-blue-200 p-6 rounded-lg"
              style={{ display: "block", visibility: "visible" }}
            >
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Camera className="h-5 w-5 mr-2" />
                Fotos e Vídeo
              </h2>

              {/* Upload de Imagens */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Fotos do Equipamento * (máximo 5 fotos)
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Camera className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <div className="text-sm text-gray-600 mb-4">
                    <label htmlFor="images" className="cursor-pointer">
                      <span className="text-primary-600 hover:text-primary-500">
                        Clique para selecionar fotos
                      </span>
                      <input
                        id="images"
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="sr-only"
                        disabled={uploadingImages || images.length >= 5}
                      />
                    </label>
                  </div>
                  {uploadingImages && (
                    <div className="text-sm text-gray-500">
                      Enviando imagens...
                    </div>
                  )}
                </div>

                {/* Preview das Imagens */}
                {images.length > 0 && (
                  <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4">
                    {images.map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={image}
                          alt={`Preview ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Upload de Vídeo */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Vídeo do Equipamento (opcional)
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Video className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <div className="text-sm text-gray-600 mb-4">
                    <label htmlFor="video" className="cursor-pointer">
                      <span className="text-primary-600 hover:text-primary-500">
                        Clique para selecionar vídeo
                      </span>
                      <input
                        id="video"
                        type="file"
                        accept="video/*"
                        onChange={handleVideoUpload}
                        className="sr-only"
                        disabled={uploadingVideo}
                      />
                    </label>
                  </div>
                  {uploadingVideo && (
                    <div className="text-sm text-gray-500">
                      Enviando vídeo...
                    </div>
                  )}
                  {video && (
                    <div className="mt-4">
                      <video
                        src={video}
                        controls
                        className="mx-auto max-w-full h-48 rounded-lg"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Botões */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => navigate("/dashboard")}
                className="btn btn-secondary"
              >
                Cancelar
              </button>
              <button
                type="submit"
                disabled={isLoading || uploadingImages || uploadingVideo}
                className="btn btn-primary"
              >
                {isLoading ? (
                  <div className="loading-spinner h-5 w-5 mr-2"></div>
                ) : null}
                Cadastrar Equipamento
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddEquipment;
