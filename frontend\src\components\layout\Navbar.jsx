import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useAuth } from "../../contexts/AuthContext";
import { useCompare } from "../../contexts/CompareContext";
import {
  Menu,
  X,
  User,
  Settings,
  LogOut,
  Shield,
  BarChart3,
  Plus,
  Package,
  Calendar,
  Heart,
} from "lucide-react";
import LanguageSelector from "../LanguageSelector";
import NotificationBell from "../notifications/NotificationBell";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { user, isAuthenticated, logout } = useAuth();
  const { compareCount, getCompareUrl } = useCompare();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate("/");
    setShowUserMenu(false);
  };

  return (
    <nav className="bg-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0 flex items-center">
              <span className="text-2xl font-bold text-primary-600">
                RentHub
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              to="/equipment"
              className="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {t("navigation.equipment")}
            </Link>

            <Link
              to={getCompareUrl()}
              className="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors relative"
            >
              <div className="flex items-center">
                <BarChart3 className="w-4 h-4 mr-1" />
                Comparar
                {compareCount > 0 && (
                  <span className="ml-1 bg-primary-600 text-white text-xs rounded-full px-2 py-0.5">
                    {compareCount}
                  </span>
                )}
              </div>
            </Link>

            {isAuthenticated ? (
              <>
                <Link
                  to="/rentals"
                  className="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  {t("navigation.rentals")}
                </Link>

                {user?.role === "admin" && (
                  <Link
                    to="/admin"
                    className="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center"
                  >
                    <Shield className="w-4 h-4 mr-1" />
                    {t("navigation.admin")}
                  </Link>
                )}

                {/* Notifications */}
                <NotificationBell />

                {/* Language Selector */}
                <LanguageSelector />

                {/* User Menu */}
                <div className="relative">
                  <button
                    onClick={() => setShowUserMenu(!showUserMenu)}
                    className="flex items-center text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    <User className="w-4 h-4 mr-1" />
                    {user?.firstName}
                  </button>

                  {showUserMenu && (
                    <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg py-1 z-50 border">
                      <Link
                        to="/add-equipment"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Adicionar Equipamento
                      </Link>

                      <Link
                        to="/my-equipment"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <Package className="w-4 h-4 mr-2" />
                        Meus Equipamentos
                      </Link>

                      <Link
                        to="/rentals"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <Calendar className="w-4 h-4 mr-2" />
                        Meus Aluguéis
                      </Link>

                      <div className="border-t border-gray-100 my-1"></div>

                      <Link
                        to="/profile"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <Settings className="w-4 h-4 mr-2" />
                        Meu Perfil
                      </Link>

                      <button
                        onClick={handleLogout}
                        className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <LogOut className="w-4 h-4 mr-2" />
                        Sair
                      </button>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <div className="flex items-center space-x-4">
                <LanguageSelector />
                <Link
                  to="/login"
                  className="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  {t("auth.login")}
                </Link>
                <Link to="/register" className="btn btn-primary">
                  {t("auth.register")}
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-gray-700 hover:text-primary-600 focus:outline-none focus:text-primary-600"
            >
              {isOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              <Link
                to="/equipment"
                className="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsOpen(false)}
              >
                Browse Equipment
              </Link>

              <Link
                to={getCompareUrl()}
                className="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsOpen(false)}
              >
                <div className="flex items-center">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Comparar
                  {compareCount > 0 && (
                    <span className="ml-2 bg-primary-600 text-white text-xs rounded-full px-2 py-0.5">
                      {compareCount}
                    </span>
                  )}
                </div>
              </Link>

              {isAuthenticated ? (
                <>
                  <Link
                    to="/add-equipment"
                    className="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex items-center">
                      <Plus className="w-4 h-4 mr-2" />
                      Adicionar Equipamento
                    </div>
                  </Link>

                  <Link
                    to="/my-equipment"
                    className="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex items-center">
                      <Package className="w-4 h-4 mr-2" />
                      Meus Equipamentos
                    </div>
                  </Link>

                  <Link
                    to="/rentals"
                    className="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      Meus Aluguéis
                    </div>
                  </Link>

                  {user?.role === "admin" && (
                    <Link
                      to="/admin"
                      className="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium"
                      onClick={() => setIsOpen(false)}
                    >
                      <div className="flex items-center">
                        <Shield className="w-4 h-4 mr-2" />
                        Admin Dashboard
                      </div>
                    </Link>
                  )}

                  <Link
                    to="/profile"
                    className="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex items-center">
                      <Settings className="w-4 h-4 mr-2" />
                      Meu Perfil
                    </div>
                  </Link>

                  <button
                    onClick={() => {
                      handleLogout();
                      setIsOpen(false);
                    }}
                    className="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium w-full text-left"
                  >
                    Logout
                  </button>
                </>
              ) : (
                <>
                  <Link
                    to="/login"
                    className="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium"
                    onClick={() => setIsOpen(false)}
                  >
                    Login
                  </Link>
                  <Link
                    to="/register"
                    className="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium"
                    onClick={() => setIsOpen(false)}
                  >
                    Sign Up
                  </Link>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
